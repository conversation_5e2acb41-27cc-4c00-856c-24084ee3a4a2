DATABASE_URL="***********************************************************/postgres?pgbouncer=true&connect_timeout=30&pool_timeout=30"

DIRECT_URL="***********************************************************/postgres?connect_timeout=30"

# Next Auth
NEXTAUTH_SECRET="your-secret-here-change-this-in-production"
NEXTAUTH_URL="http://localhost:3000"

# Discord OAuth (optional - get from https://discord.com/developers/applications)
DISCORD_CLIENT_ID=""
DISCORD_CLIENT_SECRET=""

# Google OAuth (optional - get from https://console.cloud.google.com)
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# Redis Configuration (Redis Cloud)
REDIS_HOST="your-redis-host.redns.redis-cloud.com"
REDIS_PORT="your-redis-port"
REDIS_USERNAME="your-redis-username"
REDIS_PASSWORD="your-redis-password"
REDIS_DB="your-database-name"

# Supabase Configuration (for file storage)
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Client-side Supabase (same as above but with NEXT_PUBLIC prefix)
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"

# WebSocket URL (Optional)
NEXT_PUBLIC_WS_URL="ws://localhost:3001"

# Skip env validation during build (development only)
SKIP_ENV_VALIDATION=true
