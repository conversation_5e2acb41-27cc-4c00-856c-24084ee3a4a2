import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    message: 'Test API endpoint',
    timestamp: new Date().toISOString(),
    headers: Object.fromEntries(request.headers.entries())
  })
}

export async function HEAD(request: NextRequest) {
  const response = new NextResponse(null, { status: 200 })
  
  // Copy middleware headers to response if they exist
  const middlewareHeaders = [
    'X-Middleware-Processed',
    'X-Middleware-Path', 
    'X-Middleware-Timestamp'
  ]
  
  middlewareHeaders.forEach(header => {
    const value = request.headers.get(header)
    if (value) {
      response.headers.set(header, value)
    }
  })
  
  return response
}
