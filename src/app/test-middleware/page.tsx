'use client'

import { useEffect, useState } from 'react'

interface MiddlewareHeaders {
  processed: string | null
  path: string | null
  timestamp: string | null
}

export default function TestMiddlewarePage() {
  const [headers, setHeaders] = useState<MiddlewareHeaders>({
    processed: null,
    path: null,
    timestamp: null
  })

  useEffect(() => {
    // Check if middleware headers are present by making a request to get current headers
    const checkHeaders = async () => {
      try {
        const response = await fetch('/api/test', { method: 'HEAD' })
        setHeaders({
          processed: response.headers.get('X-Middleware-Processed'),
          path: response.headers.get('X-Middleware-Path'),
          timestamp: response.headers.get('X-Middleware-Timestamp')
        })
      } catch (error) {
        console.error('Error checking headers:', error)
      }
    }

    checkHeaders()
  }, [])

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-4">Middleware Test Page</h1>
      <p className="text-lg mb-4">
        If you can see this page, the middleware is working correctly and allowing the request to pass through.
      </p>

      <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
        <strong>Success!</strong> Middleware is functioning properly.
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Middleware Headers</h2>
        <div className="space-y-2">
          <div className="flex items-center">
            <span className="font-medium w-32">Processed:</span>
            <span className={`px-2 py-1 rounded text-sm ${
              headers.processed === 'true'
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {headers.processed || 'Not detected'}
            </span>
          </div>
          <div className="flex items-center">
            <span className="font-medium w-32">Path:</span>
            <span className="text-gray-700">{headers.path || 'Not available'}</span>
          </div>
          <div className="flex items-center">
            <span className="font-medium w-32">Timestamp:</span>
            <span className="text-gray-700">{headers.timestamp || 'Not available'}</span>
          </div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">How to verify middleware is working:</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Open browser developer tools (F12)</li>
          <li>Go to Network tab</li>
          <li>Refresh this page</li>
          <li>Look for the request to this page</li>
          <li>Check the Response Headers for X-Middleware-* headers</li>
        </ol>
      </div>
    </div>
  )
}
