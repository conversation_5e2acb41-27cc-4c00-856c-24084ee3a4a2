import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for API routes, static files, and auth pages
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/auth') ||
    pathname === '/favicon.ico' ||
    pathname.startsWith('/static/')
  ) {
    return NextResponse.next()
  }

  // Add custom headers to verify middleware is working
  const response = NextResponse.next()

  // Add custom headers to show middleware is processing the request
  response.headers.set('X-Middleware-Processed', 'true')
  response.headers.set('X-Middleware-Path', pathname)
  response.headers.set('X-Middleware-Timestamp', new Date().toISOString())

  // Log to console (note: Edge Runtime console logs may not always appear in dev server)
  console.log(`[Middleware] Processing: ${pathname} at ${new Date().toISOString()}`)

  // Example: Redirect specific test path to demonstrate middleware is working
  if (pathname === '/middleware-redirect-test') {
    console.log(`[Middleware] Redirecting ${pathname} to /test-middleware`)
    return NextResponse.redirect(new URL('/test-middleware?redirected=true', request.url))
  }

  // Add a special header for test pages to prove middleware is working
  if (pathname.includes('test-middleware')) {
    response.headers.set('X-Middleware-Test', 'middleware-is-working')
  }

  // Example: Redirect to auth if not authenticated
  // Uncomment the lines below if you want to redirect all requests to /auth
  // if (!request.cookies.get('next-auth.session-token')) {
  //   return NextResponse.redirect(new URL('/auth', request.url))
  // }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
  // Explicitly set runtime to edge (default for middleware)
  // Note: Middleware always runs on Edge Runtime in Next.js 13+
  runtime: 'edge',
}
